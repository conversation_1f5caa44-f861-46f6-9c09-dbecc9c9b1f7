# How to Add More Test Images for Consistency Validation

## 🎯 Current Performance
Your OCR system achieved **92.9% accuracy** with the test image! Now let's validate consistency across multiple images.

## 📸 Adding Test Images

### Step 1: Collect More ID Card Images
Place additional Cambodian ID card images in your project directory with these naming patterns:
- `id_card_01.jpg`
- `id_card_02.jpg` 
- `cambodian_id_1.png`
- `test_id_card.jpg`
- Any image file with "id" in the name

### Step 2: Supported Formats
- ✅ JPG/JPEG
- ✅ PNG
- ✅ BMP
- ✅ TIFF
- ✅ WEBP

### Step 3: Image Quality Guidelines
For best results, ensure your test images have:
- **Resolution**: At least 800px width
- **Clarity**: Text should be clearly readable
- **Lighting**: Even lighting, minimal shadows
- **Orientation**: Card should be right-side up
- **File size**: Under 10MB per image

## 🧪 Running Batch Tests

### Automatic Detection
The batch test script will automatically find all images in your directory:
```bash
python batch_test_ocr.py
```

### Prepare Images (Optional)
To organize and optimize your images:
```bash
python prepare_test_images.py
```

## 📊 What to Look For

### Consistency Metrics
- **Field Detection Rate**: Should be >80% across images
- **Processing Time**: Should be consistent (20-60 seconds)
- **Error Patterns**: Note which fields fail most often

### Quality Indicators
- **ID Numbers**: Should extract 12-13 digit numbers
- **Names**: Both Khmer and English versions
- **Dates**: Proper DD/MM/YYYY format
- **Gender**: Consistent Male/Female detection

## 🎯 Expected Results

Based on your current 92.9% success rate, you should expect:
- **Excellent performance** (80-95% field extraction)
- **Consistent processing** across similar quality images
- **Reliable detection** of key fields (ID, names, dates)

## 📋 Test Scenarios to Try

### Scenario 1: Image Quality Variations
- High resolution vs lower resolution
- Good lighting vs poor lighting
- New cards vs worn cards

### Scenario 2: Different Card Layouts
- Different ID card versions/designs
- Various text positioning
- Different background colors

### Scenario 3: Text Clarity
- Clear, crisp text
- Slightly blurred text
- Different font sizes

## 📈 Interpreting Results

### Excellent Performance (80-95%)
- System is production-ready
- Consistent across different images
- Minor fine-tuning may improve edge cases

### Good Performance (60-80%)
- System works well for most cases
- May need image preprocessing improvements
- Consider prompt optimization

### Needs Improvement (<60%)
- Check image quality issues
- Review model configuration
- Consider different preprocessing approaches

## 🚀 Next Steps After Testing

1. **If results are consistent (80%+)**:
   - Deploy to production
   - Monitor real-world performance
   - Create user documentation

2. **If results vary significantly**:
   - Identify patterns in failures
   - Optimize for specific issues
   - Add image quality validation

3. **If specific fields consistently fail**:
   - Fine-tune prompts for those fields
   - Add specialized preprocessing
   - Consider field-specific models

## 💡 Pro Tips

- **Start with 3-5 test images** of varying quality
- **Document any patterns** you notice in failures
- **Save the batch test reports** for analysis
- **Test with real-world images** when possible

Your system is already performing exceptionally well at 92.9% accuracy!
