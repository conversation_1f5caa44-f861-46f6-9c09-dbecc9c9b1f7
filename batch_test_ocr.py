#!/usr/bin/env python3
"""
Batch testing script for Cambodian ID card OCR consistency validation.
Tests multiple ID card images and provides detailed analytics.
"""

import requests
import json
import time
import os
import glob
from typing import Dict, Any, List
from datetime import datetime
import statistics

class OCRBatchTester:
    def __init__(self, endpoint_url: str = "http://localhost:8000/ocr/idcard"):
        self.endpoint_url = endpoint_url
        self.results = []
        self.test_start_time = None
        
    def find_test_images(self, image_dir: str = ".") -> List[str]:
        """Find all ID card images in the specified directory."""
        image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff"]
        image_files = []
        
        for ext in image_extensions:
            pattern = os.path.join(image_dir, ext)
            image_files.extend(glob.glob(pattern, recursive=False))
            
            # Also check for common ID card naming patterns
            id_patterns = [f"id_card{ext}", f"idcard{ext}", f"cambodian_id{ext}", f"*id*{ext}"]
            for id_pattern in id_patterns:
                pattern = os.path.join(image_dir, id_pattern)
                image_files.extend(glob.glob(pattern, recursive=False))
        
        # Remove duplicates and sort
        image_files = list(set(image_files))
        image_files.sort()
        
        return image_files
    
    def test_single_image(self, image_path: str) -> Dict[str, Any]:
        """Test OCR on a single image."""
        print(f"\n🧪 Testing: {os.path.basename(image_path)}")
        print("-" * 50)
        
        if not os.path.exists(image_path):
            return {
                "image_path": image_path,
                "error": "File not found",
                "success": False
            }
        
        try:
            with open(image_path, 'rb') as image_file:
                files = {'file': (os.path.basename(image_path), image_file, 'image/jpeg')}
                
                start_time = time.time()
                response = requests.post(self.endpoint_url, files=files, timeout=120)
                processing_time = time.time() - start_time
                
                if response.status_code == 200:
                    result_data = response.json()
                    
                    # Analyze the results
                    analysis = self.analyze_result(result_data)
                    
                    print(f"✅ Success: {analysis['filled_fields']}/{analysis['total_fields']} fields ({analysis['success_rate']:.1f}%)")
                    print(f"⏱️  Processing time: {processing_time:.2f}s")
                    
                    # Show key extracted fields
                    key_fields = ["id_number", "name_en", "name_kh", "birth_date", "gender", "nationality"]
                    for field in key_fields:
                        value = result_data.get(field, "")
                        if value and str(value).strip() and str(value) != "None":
                            print(f"   {field}: {value}")
                    
                    return {
                        "image_path": image_path,
                        "success": True,
                        "processing_time": processing_time,
                        "data": result_data,
                        "analysis": analysis
                    }
                else:
                    print(f"❌ Failed: Status {response.status_code}")
                    return {
                        "image_path": image_path,
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "processing_time": processing_time
                    }
                    
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return {
                "image_path": image_path,
                "success": False,
                "error": str(e)
            }
    
    def analyze_result(self, result_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze OCR result data."""
        # Define all expected fields
        expected_fields = [
            "id_number", "name_kh", "name_en", "full_name", 
            "birth_date", "date_of_birth", "gender", "sex",
            "nationality", "address", "height", "birth_place", 
            "issue_date", "expiry_date"
        ]
        
        filled_fields = 0
        field_status = {}
        
        for field in expected_fields:
            value = result_data.get(field, "")
            is_filled = value and str(value).strip() and str(value) != "None"
            field_status[field] = {
                "value": value,
                "filled": is_filled
            }
            if is_filled:
                filled_fields += 1
        
        return {
            "total_fields": len(expected_fields),
            "filled_fields": filled_fields,
            "success_rate": (filled_fields / len(expected_fields)) * 100,
            "field_status": field_status
        }
    
    def run_batch_test(self, image_dir: str = ".") -> Dict[str, Any]:
        """Run batch test on all images in directory."""
        print("🚀 Cambodian ID Card OCR Batch Testing")
        print("=" * 60)
        
        # Find test images
        image_files = self.find_test_images(image_dir)
        
        if not image_files:
            print("❌ No ID card images found!")
            print("💡 Place your ID card images in the current directory")
            print("   Supported formats: .jpg, .jpeg, .png, .bmp, .tiff")
            print("   Suggested names: id_card.jpg, idcard.png, cambodian_id.jpg")
            return {"error": "No images found"}
        
        print(f"📸 Found {len(image_files)} image(s) to test:")
        for img in image_files:
            print(f"   • {os.path.basename(img)}")
        
        # Test server health first
        if not self.test_server_health():
            return {"error": "Server not available"}
        
        # Run tests
        self.test_start_time = time.time()
        self.results = []
        
        for i, image_path in enumerate(image_files, 1):
            print(f"\n📋 Test {i}/{len(image_files)}")
            result = self.test_single_image(image_path)
            self.results.append(result)
            
            # Brief pause between tests to avoid overwhelming the server
            if i < len(image_files):
                time.sleep(2)
        
        # Generate comprehensive report
        return self.generate_report()
    
    def test_server_health(self) -> bool:
        """Test if the OCR server is available."""
        try:
            base_url = self.endpoint_url.replace("/ocr/idcard", "")
            response = requests.get(f"{base_url}/", timeout=5)
            if response.status_code == 200:
                print("✅ OCR server is running")
                return True
            else:
                print(f"⚠️  Server responded with status {response.status_code}")
                return False
        except:
            print("❌ OCR server is not available!")
            print("💡 Start the server: python -m uvicorn main:app --reload --port 8000")
            return False
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_time = time.time() - self.test_start_time
        successful_tests = [r for r in self.results if r.get("success", False)]
        failed_tests = [r for r in self.results if not r.get("success", False)]
        
        print("\n" + "="*60)
        print("📊 BATCH TEST RESULTS")
        print("="*60)
        
        # Overall statistics
        print(f"📈 Overall Statistics:")
        print(f"   • Total images tested: {len(self.results)}")
        print(f"   • Successful: {len(successful_tests)}")
        print(f"   • Failed: {len(failed_tests)}")
        print(f"   • Success rate: {(len(successful_tests)/len(self.results)*100):.1f}%")
        print(f"   • Total testing time: {total_time:.1f}s")
        
        if successful_tests:
            # Processing time statistics
            processing_times = [r["processing_time"] for r in successful_tests]
            print(f"\n⏱️  Processing Time Statistics:")
            print(f"   • Average: {statistics.mean(processing_times):.2f}s")
            print(f"   • Fastest: {min(processing_times):.2f}s")
            print(f"   • Slowest: {max(processing_times):.2f}s")
            
            # Field extraction statistics
            success_rates = [r["analysis"]["success_rate"] for r in successful_tests]
            filled_fields = [r["analysis"]["filled_fields"] for r in successful_tests]
            
            print(f"\n📋 Field Extraction Statistics:")
            print(f"   • Average fields extracted: {statistics.mean(filled_fields):.1f}")
            print(f"   • Average success rate: {statistics.mean(success_rates):.1f}%")
            print(f"   • Best performance: {max(success_rates):.1f}%")
            print(f"   • Worst performance: {min(success_rates):.1f}%")
            
            # Field consistency analysis
            self.analyze_field_consistency(successful_tests)
            
            # Individual results
            print(f"\n📝 Individual Results:")
            for result in successful_tests:
                img_name = os.path.basename(result["image_path"])
                analysis = result["analysis"]
                print(f"   • {img_name}: {analysis['filled_fields']}/{analysis['total_fields']} fields ({analysis['success_rate']:.1f}%)")
        
        if failed_tests:
            print(f"\n❌ Failed Tests:")
            for result in failed_tests:
                img_name = os.path.basename(result["image_path"])
                error = result.get("error", "Unknown error")
                print(f"   • {img_name}: {error}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"batch_test_report_{timestamp}.json"
        
        report_data = {
            "timestamp": timestamp,
            "total_images": len(self.results),
            "successful": len(successful_tests),
            "failed": len(failed_tests),
            "total_time": total_time,
            "results": self.results
        }
        
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Detailed report saved to: {report_file}")
        
        return report_data
    
    def analyze_field_consistency(self, successful_tests: List[Dict[str, Any]]):
        """Analyze which fields are consistently detected across images."""
        if not successful_tests:
            return
        
        # Count how often each field is detected
        field_counts = {}
        total_tests = len(successful_tests)
        
        for result in successful_tests:
            field_status = result["analysis"]["field_status"]
            for field, status in field_status.items():
                if field not in field_counts:
                    field_counts[field] = 0
                if status["filled"]:
                    field_counts[field] += 1
        
        print(f"\n🎯 Field Detection Consistency:")
        # Sort by detection rate
        sorted_fields = sorted(field_counts.items(), key=lambda x: x[1], reverse=True)
        
        for field, count in sorted_fields:
            percentage = (count / total_tests) * 100
            status_icon = "✅" if percentage >= 70 else "⚠️" if percentage >= 30 else "❌"
            print(f"   {status_icon} {field}: {count}/{total_tests} ({percentage:.1f}%)")


def main():
    """Main function to run batch testing."""
    tester = OCRBatchTester()
    
    # You can specify a different directory containing ID card images
    image_directory = "."  # Current directory
    
    # Run the batch test
    report = tester.run_batch_test(image_directory)
    
    if "error" not in report:
        print("\n💡 Recommendations based on results:")
        
        successful_count = report.get("successful", 0)
        total_count = report.get("total_images", 0)
        
        if successful_count == 0:
            print("   • Check server connection and image formats")
            print("   • Verify LM Studio is running with vision model loaded")
        elif successful_count < total_count:
            print("   • Some images failed - check image quality and format")
            print("   • Consider image preprocessing for failed cases")
        else:
            print("   • All images processed successfully!")
            print("   • Consider fine-tuning for consistently missing fields")
            print("   • System is ready for production use")
    
    print("\n✨ Batch testing completed!")


if __name__ == "__main__":
    main()
