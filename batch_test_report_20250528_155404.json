{"timestamp": "20250528_155404", "total_images": 10, "successful": 10, "failed": 0, "total_time": 39.50499773025513, "results": [{"image_path": "test_variations\\01_original.jpg", "success": true, "processing_time": 2.162061929702759, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\02_darker.jpg", "success": true, "processing_time": 2.119366407394409, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\03_brighter.jpg", "success": true, "processing_time": 2.1350526809692383, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\04_low_contrast.jpg", "success": true, "processing_time": 2.228942632675171, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\05_high_contrast.jpg", "success": true, "processing_time": 2.164008140563965, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\06_slightly_blurred.jpg", "success": true, "processing_time": 2.1163268089294434, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\07_sharpened.jpg", "success": true, "processing_time": 2.1495137214660645, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\08_smaller.jpg", "success": true, "processing_time": 2.0999374389648438, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (779, 560), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (779, 560), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\09_low_quality.jpg", "success": true, "processing_time": 2.144409418106079, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}, {"image_path": "test_variations\\10_grayscale.jpg", "success": true, "processing_time": 2.1226840019226074, "data": {"full_name": null, "raw_khmer": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "raw_english": "LLM OCR Service initialized but requires vision-capable model for image processing. Image dimensions: (1114, 800), Mode: RGB", "id_number": null, "name_kh": null, "name_en": null, "birth_date": null, "date_of_birth": null, "sex": null, "gender": null, "nationality": "Cambodian", "height": null, "birth_place": null, "address": null, "issue_date": null, "expiry_date": null, "description": null, "ocr_method": "llm_vision", "model_used": "LM Studio", "confidence": 0.8, "processing_notes": "Processed with LM Studio SDK"}, "analysis": {"total_fields": 14, "filled_fields": 1, "success_rate": 7.142857142857142, "field_status": {"id_number": {"value": null, "filled": null}, "name_kh": {"value": null, "filled": null}, "name_en": {"value": null, "filled": null}, "full_name": {"value": null, "filled": null}, "birth_date": {"value": null, "filled": null}, "date_of_birth": {"value": null, "filled": null}, "gender": {"value": null, "filled": null}, "sex": {"value": null, "filled": null}, "nationality": {"value": "Cambodian", "filled": true}, "address": {"value": null, "filled": null}, "height": {"value": null, "filled": null}, "birth_place": {"value": null, "filled": null}, "issue_date": {"value": null, "filled": null}, "expiry_date": {"value": null, "filled": null}}}}]}