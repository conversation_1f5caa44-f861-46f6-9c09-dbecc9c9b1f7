#!/usr/bin/env python3
"""
Create test variations from existing ID card image to test OCR consistency.
This helps validate how the system handles different image conditions.
"""

from PIL import Image, ImageEnhance, ImageFilter
import os

def create_test_variations(input_image: str = "id_card.jpg"):
    """Create various test images from the original to test consistency."""
    
    if not os.path.exists(input_image):
        print(f"❌ Original image not found: {input_image}")
        return
    
    print(f"🖼️  Creating test variations from: {input_image}")
    print("=" * 50)
    
    try:
        # Load original image
        original = Image.open(input_image)
        print(f"📸 Original size: {original.size}")
        
        # Create test_variations directory
        test_dir = "test_variations"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
            print(f"📁 Created directory: {test_dir}")
        
        variations_created = 0
        
        # 1. Original copy
        original_path = os.path.join(test_dir, "01_original.jpg")
        original.save(original_path, "JPEG", quality=95)
        print(f"✅ Created: 01_original.jpg")
        variations_created += 1
        
        # 2. Slightly darker (simulating poor lighting)
        darker = ImageEnhance.Brightness(original).enhance(0.8)
        darker_path = os.path.join(test_dir, "02_darker.jpg")
        darker.save(darker_path, "JPEG", quality=95)
        print(f"✅ Created: 02_darker.jpg (80% brightness)")
        variations_created += 1
        
        # 3. Slightly brighter
        brighter = ImageEnhance.Brightness(original).enhance(1.2)
        brighter_path = os.path.join(test_dir, "03_brighter.jpg")
        brighter.save(brighter_path, "JPEG", quality=95)
        print(f"✅ Created: 03_brighter.jpg (120% brightness)")
        variations_created += 1
        
        # 4. Lower contrast
        low_contrast = ImageEnhance.Contrast(original).enhance(0.7)
        low_contrast_path = os.path.join(test_dir, "04_low_contrast.jpg")
        low_contrast.save(low_contrast_path, "JPEG", quality=95)
        print(f"✅ Created: 04_low_contrast.jpg (70% contrast)")
        variations_created += 1
        
        # 5. Higher contrast
        high_contrast = ImageEnhance.Contrast(original).enhance(1.3)
        high_contrast_path = os.path.join(test_dir, "05_high_contrast.jpg")
        high_contrast.save(high_contrast_path, "JPEG", quality=95)
        print(f"✅ Created: 05_high_contrast.jpg (130% contrast)")
        variations_created += 1
        
        # 6. Slightly blurred (simulating camera shake)
        blurred = original.filter(ImageFilter.GaussianBlur(radius=0.5))
        blurred_path = os.path.join(test_dir, "06_slightly_blurred.jpg")
        blurred.save(blurred_path, "JPEG", quality=95)
        print(f"✅ Created: 06_slightly_blurred.jpg (0.5px blur)")
        variations_created += 1
        
        # 7. Sharpened
        sharpened = original.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))
        sharpened_path = os.path.join(test_dir, "07_sharpened.jpg")
        sharpened.save(sharpened_path, "JPEG", quality=95)
        print(f"✅ Created: 07_sharpened.jpg (enhanced sharpness)")
        variations_created += 1
        
        # 8. Resized smaller (simulating lower resolution)
        smaller_size = (int(original.width * 0.7), int(original.height * 0.7))
        smaller = original.resize(smaller_size, Image.Resampling.LANCZOS)
        smaller_path = os.path.join(test_dir, "08_smaller.jpg")
        smaller.save(smaller_path, "JPEG", quality=95)
        print(f"✅ Created: 08_smaller.jpg ({smaller_size})")
        variations_created += 1
        
        # 9. Lower JPEG quality
        low_quality_path = os.path.join(test_dir, "09_low_quality.jpg")
        original.save(low_quality_path, "JPEG", quality=60)
        print(f"✅ Created: 09_low_quality.jpg (60% JPEG quality)")
        variations_created += 1
        
        # 10. Grayscale version
        grayscale = original.convert("L").convert("RGB")
        grayscale_path = os.path.join(test_dir, "10_grayscale.jpg")
        grayscale.save(grayscale_path, "JPEG", quality=95)
        print(f"✅ Created: 10_grayscale.jpg (grayscale)")
        variations_created += 1
        
        print(f"\n🎉 Created {variations_created} test variations in {test_dir}/")
        print(f"💡 Run batch test: python batch_test_ocr.py")
        
        return test_dir, variations_created
        
    except Exception as e:
        print(f"❌ Error creating variations: {e}")
        return None, 0

def run_batch_test_on_variations():
    """Run batch test on the created variations."""
    test_dir = "test_variations"
    
    if not os.path.exists(test_dir):
        print(f"❌ Test variations directory not found: {test_dir}")
        print("💡 Run create_test_variations() first")
        return
    
    print(f"\n🧪 Running batch test on variations in {test_dir}/")
    print("=" * 50)
    
    # Import and run the batch tester
    try:
        import sys
        sys.path.append(".")
        from batch_test_ocr import OCRBatchTester
        
        tester = OCRBatchTester()
        report = tester.run_batch_test(test_dir)
        
        if "error" not in report:
            print("\n📊 Variation Test Summary:")
            print(f"   • Total variations tested: {report.get('total_images', 0)}")
            print(f"   • Successful: {report.get('successful', 0)}")
            print(f"   • Failed: {report.get('failed', 0)}")
            
            if report.get('successful', 0) > 0:
                print("\n💡 This shows how consistent your OCR is across different image conditions!")
        
    except ImportError:
        print("❌ Could not import batch_test_ocr module")
        print("💡 Make sure you're in the correct directory")
    except Exception as e:
        print(f"❌ Error running batch test: {e}")

def main():
    """Main function."""
    print("🔬 OCR Consistency Testing with Image Variations")
    print("=" * 60)
    print("This script creates multiple versions of your ID card image")
    print("to test how consistently the OCR performs under different conditions.")
    
    # Create variations
    test_dir, count = create_test_variations()
    
    if count > 0:
        print(f"\n🎯 Test Scenarios Created:")
        print("   • Original quality")
        print("   • Different brightness levels")
        print("   • Various contrast settings")
        print("   • Blur and sharpness variations")
        print("   • Different resolutions")
        print("   • Quality variations")
        print("   • Grayscale version")
        
        print(f"\n💡 Next Steps:")
        print(f"   1. Review images in {test_dir}/ directory")
        print(f"   2. Run: python batch_test_ocr.py")
        print(f"   3. Change to {test_dir}/ and run batch test there")
        print(f"   4. Compare results across variations")
        
        # Ask if user wants to run batch test immediately
        try:
            response = input(f"\n🤖 Run batch test on variations now? (y/n): ").strip().lower()
            if response in ['y', 'yes']:
                run_batch_test_on_variations()
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")

if __name__ == "__main__":
    main()
