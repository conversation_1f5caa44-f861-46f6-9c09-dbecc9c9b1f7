#!/usr/bin/env python3
"""
Helper script to prepare and organize test images for batch OCR testing.
"""

import os
import shutil
from PIL import Image
import glob
from typing import List

def find_all_images(directory: str = ".") -> List[str]:
    """Find all image files in the directory."""
    image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff", "*.webp"]
    image_files = []
    
    for ext in image_extensions:
        pattern = os.path.join(directory, ext)
        image_files.extend(glob.glob(pattern, recursive=False))
        
        # Also check subdirectories
        pattern = os.path.join(directory, "**", ext)
        image_files.extend(glob.glob(pattern, recursive=True))
    
    return list(set(image_files))

def analyze_image(image_path: str) -> dict:
    """Analyze an image file."""
    try:
        with Image.open(image_path) as img:
            return {
                "path": image_path,
                "size": img.size,
                "mode": img.mode,
                "format": img.format,
                "file_size": os.path.getsize(image_path),
                "valid": True
            }
    except Exception as e:
        return {
            "path": image_path,
            "error": str(e),
            "valid": False
        }

def create_test_directory():
    """Create a test directory structure."""
    test_dir = "test_images"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"✅ Created directory: {test_dir}")
    else:
        print(f"📁 Directory already exists: {test_dir}")
    
    return test_dir

def copy_and_rename_images(image_files: List[str], test_dir: str):
    """Copy images to test directory with standardized names."""
    copied_count = 0
    
    for i, image_path in enumerate(image_files, 1):
        try:
            # Get file extension
            _, ext = os.path.splitext(image_path)
            
            # Create standardized name
            new_name = f"id_card_{i:02d}{ext.lower()}"
            new_path = os.path.join(test_dir, new_name)
            
            # Copy file
            shutil.copy2(image_path, new_path)
            print(f"📋 Copied: {os.path.basename(image_path)} → {new_name}")
            copied_count += 1
            
        except Exception as e:
            print(f"❌ Failed to copy {image_path}: {e}")
    
    return copied_count

def optimize_image_for_ocr(image_path: str, output_path: str = None) -> bool:
    """Optimize an image for better OCR results."""
    if output_path is None:
        name, ext = os.path.splitext(image_path)
        output_path = f"{name}_optimized{ext}"
    
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize if too large (max 2048px width)
            if img.width > 2048:
                ratio = 2048 / img.width
                new_height = int(img.height * ratio)
                img = img.resize((2048, new_height), Image.Resampling.LANCZOS)
            
            # Save with high quality
            img.save(output_path, 'JPEG', quality=95, optimize=True)
            return True
            
    except Exception as e:
        print(f"❌ Failed to optimize {image_path}: {e}")
        return False

def main():
    """Main function to prepare test images."""
    print("🖼️  Cambodian ID Card Test Image Preparation")
    print("=" * 60)
    
    # Find all images in current directory
    print("🔍 Searching for images...")
    image_files = find_all_images(".")
    
    if not image_files:
        print("❌ No image files found!")
        print("\n💡 To prepare test images:")
        print("   1. Place your Cambodian ID card images in this directory")
        print("   2. Supported formats: .jpg, .jpeg, .png, .bmp, .tiff, .webp")
        print("   3. Run this script again")
        return
    
    print(f"📸 Found {len(image_files)} image(s):")
    
    # Analyze each image
    valid_images = []
    for image_path in image_files:
        analysis = analyze_image(image_path)
        if analysis["valid"]:
            size_mb = analysis["file_size"] / (1024 * 1024)
            print(f"   ✅ {os.path.basename(image_path)}: {analysis['size']} pixels, {size_mb:.1f}MB")
            valid_images.append(image_path)
        else:
            print(f"   ❌ {os.path.basename(image_path)}: {analysis['error']}")
    
    if not valid_images:
        print("❌ No valid images found!")
        return
    
    print(f"\n📋 {len(valid_images)} valid image(s) ready for testing")
    
    # Ask user what to do
    print("\n🔧 Preparation Options:")
    print("1. Test images as-is (recommended)")
    print("2. Copy and rename images to test_images/ directory")
    print("3. Optimize images for better OCR (resize, enhance quality)")
    print("4. Exit")
    
    try:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            print("\n✅ Images are ready for testing!")
            print("💡 Run the batch test with: python batch_test_ocr.py")
            
        elif choice == "2":
            test_dir = create_test_directory()
            copied_count = copy_and_rename_images(valid_images, test_dir)
            print(f"\n✅ Copied {copied_count} images to {test_dir}/")
            print(f"💡 Run batch test with: python batch_test_ocr.py")
            
        elif choice == "3":
            print("\n🔧 Optimizing images...")
            optimized_dir = "optimized_images"
            if not os.path.exists(optimized_dir):
                os.makedirs(optimized_dir)
            
            optimized_count = 0
            for image_path in valid_images:
                name = os.path.basename(image_path)
                name_no_ext, ext = os.path.splitext(name)
                output_path = os.path.join(optimized_dir, f"{name_no_ext}_optimized{ext}")
                
                if optimize_image_for_ocr(image_path, output_path):
                    print(f"   ✅ Optimized: {name}")
                    optimized_count += 1
                else:
                    print(f"   ❌ Failed: {name}")
            
            print(f"\n✅ Optimized {optimized_count} images in {optimized_dir}/")
            print(f"💡 Test optimized images by running batch test in that directory")
            
        elif choice == "4":
            print("👋 Goodbye!")
            
        else:
            print("❌ Invalid choice!")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")

def create_sample_test_plan():
    """Create a sample test plan document."""
    test_plan = """# Cambodian ID Card OCR Test Plan

## Test Objectives
- Validate OCR accuracy across different ID card images
- Measure consistency of field extraction
- Identify performance patterns and bottlenecks

## Test Images Preparation
1. Collect diverse Cambodian ID card images:
   - Different lighting conditions
   - Various image qualities
   - Different card conditions (new, worn, etc.)
   - Multiple orientations if applicable

2. Image requirements:
   - Format: JPG, PNG preferred
   - Resolution: At least 800px width
   - Quality: Clear, readable text
   - Size: Under 10MB per image

## Expected Results
- ID Number: 12-digit number
- Names: Both Khmer and English
- Birth Date: DD/MM/YYYY format
- Gender: Male/Female
- Nationality: Usually "Cambodian" or "ខ្មែរ"
- Address: Full address in Khmer
- Dates: Issue and expiry dates

## Success Criteria
- Field extraction rate: >60%
- Processing time: <60 seconds per image
- JSON format: Valid structure
- Consistency: Similar results for similar quality images

## Test Execution
1. Run: python prepare_test_images.py
2. Run: python batch_test_ocr.py
3. Review generated reports
4. Analyze consistency patterns
"""
    
    with open("test_plan.md", "w", encoding="utf-8") as f:
        f.write(test_plan)
    
    print("📋 Created test_plan.md with detailed testing guidelines")

if __name__ == "__main__":
    main()
    
    # Also create test plan
    if not os.path.exists("test_plan.md"):
        create_sample_test_plan()
