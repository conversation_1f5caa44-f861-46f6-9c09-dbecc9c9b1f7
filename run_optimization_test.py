#!/usr/bin/env python3
"""
Comprehensive test script to validate OCR optimizations.
This script runs both the original batch test and the new optimization test to compare results.
"""

import subprocess
import sys
import json
import os
from datetime import datetime
from typing import Dict, Any

def run_command(command: str, description: str) -> Dict[str, Any]:
    """Run a command and capture its output."""
    print(f"\n🚀 {description}")
    print("=" * 60)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=600  # 10 minute timeout
        )
        
        if result.returncode == 0:
            print("✅ Command completed successfully")
            return {
                "success": True,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
        else:
            print(f"❌ Command failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            return {
                "success": False,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
    except subprocess.TimeoutExpired:
        print("⏰ Command timed out after 10 minutes")
        return {
            "success": False,
            "error": "Timeout",
            "timeout": True
        }
    except Exception as e:
        print(f"❌ Error running command: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def check_server_status() -> bool:
    """Check if the OCR server is running."""
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def find_latest_report(pattern: str) -> str:
    """Find the latest report file matching the pattern."""
    files = [f for f in os.listdir('.') if f.startswith(pattern) and f.endswith('.json')]
    if not files:
        return None
    return max(files, key=os.path.getctime)

def compare_results(original_report: str, optimized_report: str) -> Dict[str, Any]:
    """Compare results between original and optimized tests."""
    try:
        with open(original_report, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        with open(optimized_report, 'r', encoding='utf-8') as f:
            optimized_data = json.load(f)
        
        # Extract key metrics
        original_summary = original_data.get('summary', {})
        optimized_summary = optimized_data.get('summary', {})
        
        # Calculate improvements
        improvements = {}
        
        # Overall success rate
        orig_success = original_summary.get('success_rate', 0)
        opt_success = optimized_summary.get('success_rate', 0)
        improvements['success_rate'] = opt_success - orig_success
        
        # Field extraction rate
        orig_extraction = original_summary.get('avg_extraction_rate', 0)
        opt_extraction = optimized_summary.get('avg_extraction_rate', 0)
        improvements['extraction_rate'] = opt_extraction - orig_extraction
        
        # Birth place detection
        orig_birth_place = 0  # Original didn't track this specifically
        opt_birth_place = optimized_summary.get('birth_place_rate', 0)
        improvements['birth_place_rate'] = opt_birth_place - orig_birth_place
        
        # Processing time
        orig_time = original_summary.get('avg_processing_time', 0)
        opt_time = optimized_summary.get('avg_processing_time', 0)
        improvements['processing_time'] = opt_time - orig_time  # Negative is better
        
        return {
            "original": original_summary,
            "optimized": optimized_summary,
            "improvements": improvements,
            "comparison_successful": True
        }
        
    except Exception as e:
        return {
            "comparison_successful": False,
            "error": str(e)
        }

def main():
    """Main test execution function."""
    print("🧪 OCR Optimization Validation Test Suite")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if server is running
    if not check_server_status():
        print("\n❌ OCR server is not running!")
        print("💡 Please start the server first:")
        print("   python main.py")
        return
    
    print("\n✅ OCR server is running")
    
    # Step 1: Run original batch test for comparison
    print("\n📊 Step 1: Running original batch test for baseline...")
    original_result = run_command("python batch_test_ocr.py", "Original Batch Test")
    
    if not original_result["success"]:
        print("❌ Original batch test failed, continuing with optimization test only...")
    
    # Step 2: Run optimization test
    print("\n🎯 Step 2: Running optimization test...")
    optimization_result = run_command("python test_optimized_ocr.py", "Optimization Test")
    
    if not optimization_result["success"]:
        print("❌ Optimization test failed!")
        return
    
    # Step 3: Compare results if both tests succeeded
    if original_result["success"] and optimization_result["success"]:
        print("\n📈 Step 3: Comparing results...")
        
        # Find the latest report files
        original_report = find_latest_report("batch_test_report_")
        optimized_report = find_latest_report("optimization_test_report_")
        
        if original_report and optimized_report:
            comparison = compare_results(original_report, optimized_report)
            
            if comparison["comparison_successful"]:
                print("\n📊 COMPARISON RESULTS")
                print("=" * 40)
                
                improvements = comparison["improvements"]
                
                print(f"🎯 Success Rate: {improvements['success_rate']:+.1f}%")
                print(f"📋 Extraction Rate: {improvements['extraction_rate']:+.1f}%")
                print(f"🏠 Birth Place Detection: {improvements['birth_place_rate']:+.1f}%")
                print(f"⏱️  Processing Time: {improvements['processing_time']:+.2f}s")
                
                # Determine overall improvement
                key_improvements = [
                    improvements['extraction_rate'] > 0,
                    improvements['birth_place_rate'] > 0,
                    improvements['processing_time'] < 5  # Allow slight increase
                ]
                
                if sum(key_improvements) >= 2:
                    print("\n✅ OPTIMIZATION SUCCESSFUL!")
                    print("   Key improvements detected in multiple areas")
                else:
                    print("\n⚠️  MIXED RESULTS")
                    print("   Some improvements, but optimization may need refinement")
            else:
                print(f"❌ Comparison failed: {comparison['error']}")
        else:
            print("❌ Could not find report files for comparison")
    
    # Step 4: Summary and recommendations
    print("\n📝 SUMMARY AND RECOMMENDATIONS")
    print("=" * 50)
    
    if optimization_result["success"]:
        print("✅ Optimization test completed successfully")
        
        # Find and display key metrics from optimization report
        optimized_report = find_latest_report("optimization_test_report_")
        if optimized_report:
            try:
                with open(optimized_report, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                summary = data.get('summary', {})
                birth_place_rate = summary.get('birth_place_rate', 0)
                critical_rate = summary.get('critical_extraction_rate', 0)
                
                print(f"\n🎯 Key Metrics:")
                print(f"   • Birth place detection: {birth_place_rate:.1f}%")
                print(f"   • Critical fields: {critical_rate:.1f}%")
                
                if birth_place_rate > 50:
                    print("   ✅ Birth place extraction significantly improved!")
                elif birth_place_rate > 0:
                    print("   ⚠️  Birth place extraction partially improved")
                else:
                    print("   ❌ Birth place extraction still needs work")
                
                if critical_rate > 80:
                    print("   ✅ Critical fields performing well!")
                else:
                    print("   ⚠️  Critical fields need more optimization")
                    
            except Exception as e:
                print(f"❌ Error reading optimization report: {e}")
    
    print(f"\n📁 Report files:")
    if original_result["success"]:
        original_report = find_latest_report("batch_test_report_")
        if original_report:
            print(f"   • Original: {original_report}")
    
    if optimization_result["success"]:
        optimized_report = find_latest_report("optimization_test_report_")
        if optimized_report:
            print(f"   • Optimized: {optimized_report}")
    
    print(f"\n✨ Test suite completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
