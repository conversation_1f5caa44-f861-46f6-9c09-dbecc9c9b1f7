#!/usr/bin/env python3
"""
Optimized OCR test script to validate improvements in field extraction.
Focuses on testing the enhanced birth_place extraction and name field improvements.
"""

import requests
import json
import time
import os
from typing import Dict, Any, List
from datetime import datetime

class OptimizedOCRTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.ocr_endpoint = f"{base_url}/ocr/idcard"
        
    def test_server_health(self) -> bool:
        """Test if the OCR server is running."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            print("❌ OCR server not available at http://localhost:8000")
            print("💡 Start the server with: python main.py")
            return False
    
    def test_single_image(self, image_path: str) -> Dict[str, Any]:
        """Test OCR on a single image with detailed analysis."""
        print(f"\n🔍 Testing: {os.path.basename(image_path)}")
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
                
                start_time = time.time()
                response = requests.post(self.ocr_endpoint, files=files, timeout=60)
                processing_time = time.time() - start_time
                
                if response.status_code == 200:
                    result_data = response.json()
                    analysis = self.analyze_improvements(result_data)
                    
                    print(f"✅ Success: {analysis['filled_fields']}/{analysis['total_fields']} fields ({analysis['success_rate']:.1f}%)")
                    print(f"⏱️  Processing time: {processing_time:.2f}s")
                    
                    # Show critical field status
                    critical_fields = ["birth_place", "name_kh", "name_en", "id_number"]
                    for field in critical_fields:
                        value = result_data.get(field, "")
                        status = "✅" if value and str(value).strip() and str(value) != "None" else "❌"
                        print(f"   {status} {field}: {value or 'Not extracted'}")
                    
                    return {
                        "image_path": image_path,
                        "success": True,
                        "processing_time": processing_time,
                        "data": result_data,
                        "analysis": analysis
                    }
                else:
                    print(f"❌ Failed: HTTP {response.status_code}")
                    return {"image_path": image_path, "success": False, "error": response.text}
                    
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            return {"image_path": image_path, "success": False, "error": str(e)}
    
    def analyze_improvements(self, result_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze OCR results focusing on improved fields."""
        # All expected fields
        all_fields = [
            "id_number", "name_kh", "name_en", "full_name", 
            "birth_date", "date_of_birth", "gender", "sex",
            "nationality", "address", "height", "birth_place", 
            "issue_date", "expiry_date"
        ]
        
        # Critical fields that were problematic
        critical_fields = ["birth_place", "name_kh", "name_en", "full_name"]
        
        filled_fields = 0
        critical_filled = 0
        field_status = {}
        
        for field in all_fields:
            value = result_data.get(field, "")
            is_filled = value and str(value).strip() and str(value) != "None"
            field_status[field] = {
                "value": value,
                "filled": is_filled
            }
            if is_filled:
                filled_fields += 1
                if field in critical_fields:
                    critical_filled += 1
        
        return {
            "total_fields": len(all_fields),
            "filled_fields": filled_fields,
            "success_rate": (filled_fields / len(all_fields)) * 100,
            "critical_fields": len(critical_fields),
            "critical_filled": critical_filled,
            "critical_success_rate": (critical_filled / len(critical_fields)) * 100,
            "field_status": field_status
        }
    
    def run_optimization_test(self, image_dir: str = ".") -> Dict[str, Any]:
        """Run focused test on optimization improvements."""
        print("🚀 OCR Optimization Test - Focus on Birth Place & Names")
        print("=" * 60)
        
        # Find test images
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        
        for file in os.listdir(image_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(image_dir, file))
        
        if not image_files:
            print("❌ No test images found!")
            return {"error": "No images found"}
        
        print(f"📸 Found {len(image_files)} image(s) to test")
        
        # Test server health
        if not self.test_server_health():
            return {"error": "Server not available"}
        
        # Run tests
        results = []
        total_time = 0
        
        for image_path in sorted(image_files):
            result = self.test_single_image(image_path)
            results.append(result)
            if result.get("success"):
                total_time += result.get("processing_time", 0)
        
        # Calculate statistics
        successful_results = [r for r in results if r.get("success")]
        
        if not successful_results:
            print("\n❌ No successful tests!")
            return {"error": "All tests failed"}
        
        # Overall statistics
        total_tests = len(results)
        successful_tests = len(successful_results)
        success_rate = (successful_tests / total_tests) * 100
        
        # Field extraction statistics
        total_fields_extracted = sum(r["analysis"]["filled_fields"] for r in successful_results)
        total_possible_fields = sum(r["analysis"]["total_fields"] for r in successful_results)
        avg_extraction_rate = (total_fields_extracted / total_possible_fields) * 100
        
        # Critical field statistics (birth_place, names)
        total_critical_extracted = sum(r["analysis"]["critical_filled"] for r in successful_results)
        total_critical_possible = sum(r["analysis"]["critical_fields"] for r in successful_results)
        critical_extraction_rate = (total_critical_extracted / total_critical_possible) * 100
        
        # Birth place specific statistics
        birth_place_count = sum(1 for r in successful_results 
                               if r["data"].get("birth_place") and 
                               str(r["data"]["birth_place"]).strip() and 
                               str(r["data"]["birth_place"]) != "None")
        birth_place_rate = (birth_place_count / successful_tests) * 100
        
        # Processing time statistics
        processing_times = [r["processing_time"] for r in successful_results]
        avg_time = sum(processing_times) / len(processing_times)
        min_time = min(processing_times)
        max_time = max(processing_times)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 OPTIMIZATION TEST RESULTS")
        print("=" * 60)
        print(f"📈 Overall Statistics:")
        print(f"   • Total images tested: {total_tests}")
        print(f"   • Successful: {successful_tests}")
        print(f"   • Failed: {total_tests - successful_tests}")
        print(f"   • Success rate: {success_rate:.1f}%")
        print(f"   • Total testing time: {total_time:.1f}s")
        
        print(f"\n⏱️  Processing Time Statistics:")
        print(f"   • Average: {avg_time:.2f}s")
        print(f"   • Fastest: {min_time:.2f}s")
        print(f"   • Slowest: {max_time:.2f}s")
        
        print(f"\n📋 Field Extraction Statistics:")
        print(f"   • Average extraction rate: {avg_extraction_rate:.1f}%")
        print(f"   • Critical fields rate: {critical_extraction_rate:.1f}%")
        print(f"   • Birth place detection: {birth_place_count}/{successful_tests} ({birth_place_rate:.1f}%)")
        
        # Field-by-field analysis
        field_counts = {}
        for result in successful_results:
            for field, status in result["analysis"]["field_status"].items():
                if field not in field_counts:
                    field_counts[field] = 0
                if status["filled"]:
                    field_counts[field] += 1
        
        print(f"\n🎯 Field Detection Rates:")
        for field, count in sorted(field_counts.items()):
            rate = (count / successful_tests) * 100
            status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
            print(f"   {status} {field}: {count}/{successful_tests} ({rate:.1f}%)")
        
        # Save detailed report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"optimization_test_report_{timestamp}.json"
        
        report_data = {
            "test_type": "optimization_test",
            "timestamp": timestamp,
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "success_rate": success_rate,
                "avg_extraction_rate": avg_extraction_rate,
                "critical_extraction_rate": critical_extraction_rate,
                "birth_place_rate": birth_place_rate,
                "avg_processing_time": avg_time
            },
            "field_rates": {field: (count / successful_tests) * 100 
                           for field, count in field_counts.items()},
            "detailed_results": results
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Detailed report saved to: {report_file}")
        
        return report_data

def main():
    """Main test function."""
    tester = OptimizedOCRTester()
    
    # Run optimization test
    results = tester.run_optimization_test()
    
    if "error" not in results:
        print("\n✨ Optimization test completed!")
        
        # Show key improvements
        birth_place_rate = results["summary"]["birth_place_rate"]
        critical_rate = results["summary"]["critical_extraction_rate"]
        
        print(f"\n🎯 Key Improvements:")
        print(f"   • Birth place detection: {birth_place_rate:.1f}%")
        print(f"   • Critical fields (names, birth_place): {critical_rate:.1f}%")
        
        if birth_place_rate > 0:
            print("   ✅ Birth place extraction improved!")
        else:
            print("   ❌ Birth place still needs work")
            
        if critical_rate > 80:
            print("   ✅ Critical fields performing well!")
        else:
            print("   ⚠️  Critical fields need more optimization")

if __name__ == "__main__":
    main()
