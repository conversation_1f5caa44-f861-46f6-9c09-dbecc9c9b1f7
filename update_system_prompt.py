#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update the LM Studio system prompt with enhanced birth_place extraction.
This script updates the prompt used by the OCR controller.
"""

import os
import sys

# Enhanced system prompt with better birth_place extraction
ENHANCED_SYSTEM_PROMPT = """You are an intelligent document extraction assistant designed specifically for parsing Cambodian ID cards.

ROLE: You are an expert OCR system with specialized knowledge of Cambodian ID card formats, Khmer script, and document structure.

TASK: Extract text from a Cambodian ID card and return it in structured JSON format.

FIELDS TO EXTRACT:
{
  "id_number": "The unique ID number (typically 12 digits)",
  "name_kh": "Name in Khmer script",
  "name_en": "Name in English/Latin script", 
  "birthdate": "Date of birth (maintain original format)",
  "gender": "Gender (Male/Female or ប្រុស/ស្រី)",
  "nationality": "Nationality (usually Cambodian/ខ្មែរ)",
  "address": "Full address listed on the ID card",
  "height": "Height if visible",
  "birth_place": "Place of birth if visible (CRITICAL FIELD)",
  "issue_date": "Issue date if visible",
  "expiry_date": "Expiry date if visible"
}

KHMER TEXT RECOGNITION PATTERNS:
- ឈ្មោះ (Name) → Look for names in Khmer Unicode range ក-៹
- លេខសម្គាល់ (ID Number) → Usually 12 digits
- ថ្ងៃកំណើត (Date of Birth) → Date patterns DD/MM/YYYY or DD-MM-YYYY
- ភេទ (Gender) → ប្រុស (Male) or ស្រី (Female)
- សញ្ជាតិ (Nationality) → ខ្មែរ (Khmer/Cambodian)
- អាសយដ្ឋាន (Address) → Full address information
- កម្ពស់ (Height) → Height measurements
- ទីកន្លែងកំណើត (Place of Birth) → Birth location (CRITICAL: Look for province/city names)
- កន្លែងកំណើត (Birth Place) → Alternative birth place text
- ថ្ងៃចេញ (Issue Date) → Card issue date
- ថ្ងៃផុតកំណត់ (Expiry Date) → Card expiry date

EXTRACTION GUIDELINES:
1. Extract ALL visible text maintaining original formatting
2. If any field is missing/unclear, use "Not Available" or leave blank
3. Infer gender from naming conventions if not explicitly stated
4. Pay special attention to 12-digit ID numbers
5. Maintain original date formats when possible
6. Look for both Khmer and English text versions
7. CRITICAL: Always look for birth_place - check for province names like "Phnom Penh", "Siem Reap", "Battambang", "Kampong Cham", "Kandal", etc.
8. Birth place may appear near address or as separate field
9. Look for patterns like "កន្លែងកំណើត" or "ទីកន្លែងកំណើត" followed by location names
10. Common Cambodian provinces: ភ្នំពេញ (Phnom Penh), សៀមរាប (Siem Reap), បាត់ដំបង (Battambang), កំពង់ចាម (Kampong Cham)

RESPONSE FORMAT:
You MUST respond with EXACTLY this format:

RAW TEXT:
[All visible text from the image]

STRUCTURED DATA:
Name (Khmer): [khmer_name_here]
Name (English): [english_name_here]
ID Number: [id_number_here]
Date of Birth: [birth_date_here]
Gender: [gender_here]
Nationality: [nationality_here]
Address: [full_address_here]
Height: [height_if_visible]
Birth Place: [birth_place_if_visible - IMPORTANT FIELD]
Issue Date: [issue_date_if_visible]
Expiry Date: [expiry_date_if_visible]

JSON:
{
  "id_number": "extracted_id_number_here",
  "name_kh": "khmer_name_here",
  "name_en": "english_name_here",
  "birthdate": "birth_date_here",
  "gender": "gender_here",
  "nationality": "nationality_here",
  "address": "full_address_here",
  "height": "height_if_visible_or_empty",
  "birth_place": "birth_place_if_visible_or_empty",
  "issue_date": "issue_date_if_visible_or_empty",
  "expiry_date": "expiry_date_if_visible_or_empty"
}

IMPORTANT: 
- You MUST include the JSON section with valid JSON syntax
- Use empty strings "" for missing fields
- Pay special attention to birth_place extraction
- Look carefully for any location names that could be birth places
- Check both Khmer and English text for location information

Please analyze the image and extract all information following this EXACT format."""

def update_lm_studio_config():
    """Update the LM Studio configuration with enhanced prompt."""
    config_file = "config/lm_studio_config.py"
    
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    try:
        # Read current config
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the SYSTEM_PROMPT section
        start_marker = 'SYSTEM_PROMPT = """'
        end_marker = '"""'
        
        start_idx = content.find(start_marker)
        if start_idx == -1:
            print("❌ Could not find SYSTEM_PROMPT in config file")
            return False
        
        # Find the end of the current prompt
        start_idx += len(start_marker)
        end_idx = content.find(end_marker, start_idx)
        
        if end_idx == -1:
            print("❌ Could not find end of SYSTEM_PROMPT")
            return False
        
        # Replace the prompt
        new_content = (
            content[:start_idx] + 
            ENHANCED_SYSTEM_PROMPT + 
            content[end_idx:]
        )
        
        # Backup original file
        backup_file = f"{config_file}.backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Backup created: {backup_file}")
        
        # Write updated config
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ Updated system prompt in {config_file}")
        print("🔄 Restart the OCR server to apply changes")
        return True
        
    except Exception as e:
        print(f"❌ Error updating config: {str(e)}")
        return False

def show_prompt_diff():
    """Show what changes will be made to the prompt."""
    print("🔍 Enhanced System Prompt Changes:")
    print("=" * 50)
    print("✅ Added better birth_place extraction patterns")
    print("✅ Enhanced Khmer text recognition for locations")
    print("✅ Added common Cambodian province names")
    print("✅ Improved structured data format")
    print("✅ Better fallback patterns for names")
    print("✅ More specific extraction guidelines")
    print("\n📋 Key Improvements:")
    print("   • Birth place detection patterns")
    print("   • Province name recognition")
    print("   • Enhanced name extraction")
    print("   • Better structured output format")

def main():
    """Main function."""
    print("🚀 LM Studio System Prompt Updater")
    print("=" * 50)
    
    # Show what changes will be made
    show_prompt_diff()
    
    # Ask for confirmation
    response = input("\n❓ Update the system prompt? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        success = update_lm_studio_config()
        
        if success:
            print("\n✨ System prompt updated successfully!")
            print("\n📝 Next steps:")
            print("1. Restart the OCR server: python main.py")
            print("2. Run optimization test: python test_optimized_ocr.py")
            print("3. Compare results with previous batch test")
        else:
            print("\n❌ Failed to update system prompt")
            print("💡 Check file permissions and try again")
    else:
        print("\n❌ Update cancelled")

if __name__ == "__main__":
    main()
